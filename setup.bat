@echo off
echo 🌊 Northern Region Water Board (NRWB) Setup
echo ==========================================

REM Check if .env file exists
if not exist .env (
    echo 📝 Creating .env file from .env.example...
    copy .env.example .env
    echo ✅ .env file created
) else (
    echo 📝 .env file already exists
)

REM Generate application key
echo 🔑 Generating application key...
php artisan key:generate

REM Install Composer dependencies
echo 📦 Installing PHP dependencies...
composer install --no-dev --optimize-autoloader

REM Install Node.js dependencies
echo 📦 Installing Node.js dependencies...
npm install

REM Create SQLite database if needed
findstr /C:"DB_CONNECTION=sqlite" .env >nul
if %errorlevel% equ 0 (
    echo 🗄️  Creating SQLite database...
    type nul > database\database.sqlite
)

REM Run database migrations
echo 🗄️  Running database migrations...
php artisan migrate --force

REM Seed the database
echo 🌱 Seeding database with sample data...
php artisan db:seed --force

REM Create storage link
echo 🔗 Creating storage link...
php artisan storage:link

REM Build frontend assets
echo 🎨 Building frontend assets...
npm run build

echo.
echo 🎉 NRWB Laravel Application Setup Complete!
echo.
echo 📋 Default Login Credentials:
echo    Admin:      <EMAIL> / Admin123!
echo    Management: <EMAIL> / Management123!
echo    Staff:      <EMAIL> / Staff123!
echo    Customer:   <EMAIL> / Customer123!
echo.
echo 🚀 To start the development server:
echo    php artisan serve
echo    npm run dev (in another terminal)
echo.
echo 🌐 Application will be available at: http://localhost:8000
echo.
echo ⚠️  Don't forget to:
echo    1. Configure your database settings in .env
echo    2. Add your Google Maps API key to .env
echo    3. Configure CTechPay credentials for payments
echo.
pause
