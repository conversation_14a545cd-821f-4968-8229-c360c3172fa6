<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\Payment;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    /**
     * Get payments for the authenticated user
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = Payment::with(['application', 'user'])
            ->when($user->isCustomer(), function ($q) use ($user) {
                return $q->where('user_id', $user->id);
            })
            ->when($request->status, function ($q, $status) {
                return $q->where('status', $status);
            })
            ->when($request->payment_method, function ($q, $method) {
                return $q->where('payment_method', $method);
            })
            ->when($request->application_id, function ($q, $applicationId) {
                return $q->where('application_id', $applicationId);
            });

        $payments = $query->orderBy('created_at', 'desc')
                         ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $payments
        ]);
    }

    /**
     * Process a new payment
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'application_id' => 'required|exists:applications,id',
            'payment_method' => 'required|in:airtel_money,tnm_mpamba',
            'payment_type' => 'required|in:application_fee,connection_fee,reconnection_fee,upgrade_fee',
            'phone_number' => 'required|string|regex:/^(\+265|265|0)?[1-9][0-9]{7,8}$/',
            'payer_name' => 'nullable|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $application = Application::findOrFail($request->application_id);
        $user = $request->user();

        // Check if user can make payment for this application
        if ($user->isCustomer() && $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to make payment for this application'
            ], 403);
        }

        // Check if application requires payment
        if (!$application->requiresPayment()) {
            return response()->json([
                'success' => false,
                'message' => 'This application does not require payment at this time'
            ], 422);
        }

        // Check if there's already a pending payment
        $existingPayment = Payment::where('application_id', $application->id)
                                 ->where('status', Payment::STATUS_PENDING)
                                 ->first();

        if ($existingPayment) {
            return response()->json([
                'success' => false,
                'message' => 'There is already a pending payment for this application'
            ], 422);
        }

        // Determine payment amount based on type
        $amount = match($request->payment_type) {
            'application_fee' => $application->application_fee,
            'connection_fee' => $application->connection_fee,
            'reconnection_fee' => $application->connection_fee,
            'upgrade_fee' => $application->connection_fee * 0.5, // 50% of connection fee
            default => $application->total_amount,
        };

        $paymentData = [
            'amount' => $amount,
            'payment_method' => $request->payment_method,
            'payment_type' => $request->payment_type,
            'phone_number' => $request->phone_number,
            'payer_name' => $request->payer_name,
            'description' => "Payment for {$application->application_number} - " . ucfirst(str_replace('_', ' ', $request->payment_type)),
        ];

        $result = $this->paymentService->processPayment($application, $paymentData);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Payment initiated successfully',
                'data' => [
                    'payment' => $result['payment'],
                    'gateway_response' => $result['gateway_response'],
                ]
            ], 201);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
                'data' => [
                    'payment' => $result['payment'],
                ]
            ], 422);
        }
    }

    /**
     * Get a specific payment
     */
    public function show(Request $request, Payment $payment)
    {
        $user = $request->user();

        // Check if user can view this payment
        if ($user->isCustomer() && $payment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this payment'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $payment->load(['application', 'user'])
        ]);
    }

    /**
     * Handle payment webhook
     */
    public function webhook(Request $request)
    {
        $webhookData = $request->all();
        
        $result = $this->paymentService->handleWebhook($webhookData);

        if ($result) {
            return response()->json(['success' => true], 200);
        } else {
            return response()->json(['success' => false], 400);
        }
    }

    /**
     * Check payment status
     */
    public function checkStatus(Request $request, Payment $payment)
    {
        $user = $request->user();

        // Check if user can check this payment status
        if ($user->isCustomer() && $payment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to check this payment status'
            ], 403);
        }

        $gatewayStatus = $this->paymentService->getPaymentStatus($payment);

        return response()->json([
            'success' => true,
            'data' => [
                'payment' => $payment->fresh(),
                'gateway_status' => $gatewayStatus,
            ]
        ]);
    }

    /**
     * Download payment receipt
     */
    public function receipt(Request $request, Payment $payment)
    {
        $user = $request->user();

        // Check if user can download this receipt
        if ($user->isCustomer() && $payment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to download this receipt'
            ], 403);
        }

        if (!$payment->isSuccessful()) {
            return response()->json([
                'success' => false,
                'message' => 'Receipt is only available for successful payments'
            ], 422);
        }

        $receiptData = $this->paymentService->generateReceipt($payment);

        return response()->json([
            'success' => true,
            'data' => $receiptData
        ]);
    }

    /**
     * Cancel a pending payment
     */
    public function cancel(Request $request, Payment $payment)
    {
        $user = $request->user();

        // Check if user can cancel this payment
        if ($user->isCustomer() && $payment->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to cancel this payment'
            ], 403);
        }

        if (!$payment->isPending()) {
            return response()->json([
                'success' => false,
                'message' => 'Only pending payments can be cancelled'
            ], 422);
        }

        $payment->update([
            'status' => Payment::STATUS_CANCELLED,
            'failure_reason' => 'Cancelled by user',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Payment cancelled successfully',
            'data' => $payment->fresh()
        ]);
    }

    /**
     * Get payment statistics (staff only)
     */
    public function statistics(Request $request)
    {
        $stats = [
            'total_payments' => Payment::count(),
            'total_amount' => Payment::where('status', Payment::STATUS_COMPLETED)->sum('amount'),
            'by_status' => Payment::selectRaw('status, COUNT(*) as count, SUM(amount) as total_amount')
                                 ->groupBy('status')
                                 ->get()
                                 ->keyBy('status'),
            'by_method' => Payment::selectRaw('payment_method, COUNT(*) as count, SUM(amount) as total_amount')
                                 ->where('status', Payment::STATUS_COMPLETED)
                                 ->groupBy('payment_method')
                                 ->get()
                                 ->keyBy('payment_method'),
            'recent' => Payment::where('created_at', '>=', now()->subDays(30))->count(),
            'today' => Payment::whereDate('created_at', today())->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }
}
