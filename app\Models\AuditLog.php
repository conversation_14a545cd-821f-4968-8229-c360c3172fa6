<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AuditLog extends Model
{
    use HasFactory;

    // Action types
    const ACTION_CREATE = 'create';
    const ACTION_UPDATE = 'update';
    const ACTION_DELETE = 'delete';
    const ACTION_LOGIN = 'login';
    const ACTION_LOGOUT = 'logout';
    const ACTION_PAYMENT = 'payment';
    const ACTION_STATUS_CHANGE = 'status_change';
    const ACTION_FILE_UPLOAD = 'file_upload';
    const ACTION_FILE_DELETE = 'file_delete';

    protected $fillable = [
        'user_id',
        'action',
        'model_type',
        'model_id',
        'description',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'performed_at',
    ];

    protected function casts(): array
    {
        return [
            'old_values' => 'array',
            'new_values' => 'array',
            'performed_at' => 'datetime',
        ];
    }

    /**
     * Get the user who performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get available actions
     */
    public static function getActions(): array
    {
        return [
            self::ACTION_CREATE => 'Create',
            self::ACTION_UPDATE => 'Update',
            self::ACTION_DELETE => 'Delete',
            self::ACTION_LOGIN => 'Login',
            self::ACTION_LOGOUT => 'Logout',
            self::ACTION_PAYMENT => 'Payment',
            self::ACTION_STATUS_CHANGE => 'Status Change',
            self::ACTION_FILE_UPLOAD => 'File Upload',
            self::ACTION_FILE_DELETE => 'File Delete',
        ];
    }

    /**
     * Log an action
     */
    public static function logAction(
        string $action,
        ?User $user = null,
        ?string $modelType = null,
        ?int $modelId = null,
        ?string $description = null,
        ?array $oldValues = null,
        ?array $newValues = null
    ): self {
        return self::create([
            'user_id' => $user?->id,
            'action' => $action,
            'model_type' => $modelType,
            'model_id' => $modelId,
            'description' => $description,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'performed_at' => now(),
        ]);
    }

    /**
     * Get action color for UI
     */
    public function getActionColor(): string
    {
        return match($this->action) {
            self::ACTION_CREATE => 'green',
            self::ACTION_UPDATE => 'blue',
            self::ACTION_DELETE => 'red',
            self::ACTION_LOGIN => 'green',
            self::ACTION_LOGOUT => 'gray',
            self::ACTION_PAYMENT => 'purple',
            self::ACTION_STATUS_CHANGE => 'yellow',
            self::ACTION_FILE_UPLOAD => 'blue',
            self::ACTION_FILE_DELETE => 'red',
            default => 'gray',
        };
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($auditLog) {
            if (empty($auditLog->performed_at)) {
                $auditLog->performed_at = now();
            }
        });
    }
}
