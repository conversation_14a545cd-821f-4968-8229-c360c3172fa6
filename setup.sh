#!/bin/bash

# NRWB Laravel Application Setup Script
echo "🌊 Northern Region Water Board (NRWB) Setup"
echo "=========================================="

# Check if .env file exists
if [ ! -f .env ]; then
    echo "📝 Creating .env file from .env.example..."
    cp .env.example .env
    echo "✅ .env file created"
else
    echo "📝 .env file already exists"
fi

# Generate application key
echo "🔑 Generating application key..."
php artisan key:generate

# Install Composer dependencies
echo "📦 Installing PHP dependencies..."
composer install --no-dev --optimize-autoloader

# Install Node.js dependencies
echo "📦 Installing Node.js dependencies..."
npm install

# Create database if using SQLite
if grep -q "DB_CONNECTION=sqlite" .env; then
    echo "🗄️  Creating SQLite database..."
    touch database/database.sqlite
fi

# Run database migrations
echo "🗄️  Running database migrations..."
php artisan migrate --force

# Seed the database
echo "🌱 Seeding database with sample data..."
php artisan db:seed --force

# Create storage link
echo "🔗 Creating storage link..."
php artisan storage:link

# Build frontend assets
echo "🎨 Building frontend assets..."
npm run build

# Set permissions (for production)
if [ "$1" = "production" ]; then
    echo "🔒 Setting production permissions..."
    chmod -R 755 storage bootstrap/cache
    chown -R www-data:www-data storage bootstrap/cache
fi

echo ""
echo "🎉 NRWB Laravel Application Setup Complete!"
echo ""
echo "📋 Default Login Credentials:"
echo "   Admin:      <EMAIL> / Admin123!"
echo "   Management: <EMAIL> / Management123!"
echo "   Staff:      <EMAIL> / Staff123!"
echo "   Customer:   <EMAIL> / Customer123!"
echo ""
echo "🚀 To start the development server:"
echo "   php artisan serve"
echo "   npm run dev (in another terminal)"
echo ""
echo "🌐 Application will be available at: http://localhost:8000"
echo ""
echo "⚠️  Don't forget to:"
echo "   1. Configure your database settings in .env"
echo "   2. Add your Google Maps API key to .env"
echo "   3. Configure CTechPay credentials for payments"
echo ""
