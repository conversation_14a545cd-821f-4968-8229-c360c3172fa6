import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { dashboard } from '@/routes';
import { type BreadcrumbItem, type User } from '@/types';
import { Head } from '@inertiajs/react';
import {
    DropletIcon,
    FileTextIcon,
    CreditCardIcon,
    ClockIcon,
    CheckCircleIcon,
    AlertCircleIcon,
    PlusIcon,
    MapPinIcon
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: dashboard().url,
    },
];

interface DashboardProps {
    auth: {
        user: User;
    };
    stats?: {
        applications: {
            total: number;
            pending: number;
            approved: number;
            completed: number;
        };
        payments: {
            total: number;
            pending: number;
            completed: number;
            total_amount: number;
        };
    };
    recent_applications?: any[];
    recent_payments?: any[];
}

export default function Dashboard({ auth, stats, recent_applications = [], recent_payments = [] }: DashboardProps) {
    const user = auth.user;
    const isCustomer = user.role === 'customer';
    const isStaff = ['staff', 'admin', 'management'].includes(user.role);

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="NRWB Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 p-6">
                {/* Welcome Section */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">
                            Welcome back, {user.name}
                        </h1>
                        <p className="text-muted-foreground">
                            {isCustomer && "Manage your water connection applications and payments"}
                            {isStaff && "Monitor applications, payments, and system activities"}
                        </p>
                    </div>
                    {isCustomer && (
                        <Button size="lg" className="gap-2">
                            <PlusIcon className="h-4 w-4" />
                            New Application
                        </Button>
                    )}
                </div>

                {/* Stats Cards */}
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                {isCustomer ? 'My Applications' : 'Total Applications'}
                            </CardTitle>
                            <FileTextIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.applications.total || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                {stats?.applications.pending || 0} pending review
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">
                                {isCustomer ? 'My Payments' : 'Total Payments'}
                            </CardTitle>
                            <CreditCardIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.payments.total || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                MWK {(stats?.payments.total_amount || 0).toLocaleString()}
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Pending Actions</CardTitle>
                            <ClockIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">
                                {(stats?.applications.pending || 0) + (stats?.payments.pending || 0)}
                            </div>
                            <p className="text-xs text-muted-foreground">
                                Require your attention
                            </p>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                            <CardTitle className="text-sm font-medium">Completed</CardTitle>
                            <CheckCircleIcon className="h-4 w-4 text-muted-foreground" />
                        </CardHeader>
                        <CardContent>
                            <div className="text-2xl font-bold">{stats?.applications.completed || 0}</div>
                            <p className="text-xs text-muted-foreground">
                                Successful connections
                            </p>
                        </CardContent>
                    </Card>
                </div>

                {/* Recent Activity */}
                <div className="grid gap-4 md:grid-cols-2">
                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Applications</CardTitle>
                            <CardDescription>
                                Your latest water connection applications
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recent_applications.length > 0 ? (
                                <div className="space-y-4">
                                    {recent_applications.slice(0, 5).map((application: any) => (
                                        <div key={application.id} className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                <DropletIcon className="h-8 w-8 text-blue-500" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium truncate">
                                                    {application.application_number}
                                                </p>
                                                <p className="text-sm text-muted-foreground truncate">
                                                    {application.property_address}
                                                </p>
                                            </div>
                                            <div className="flex-shrink-0">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    application.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                    application.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-blue-100 text-blue-800'
                                                }`}>
                                                    {application.status}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-6">
                                    <FileTextIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                                    <h3 className="mt-2 text-sm font-medium">No applications yet</h3>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                        Get started by creating your first water connection application.
                                    </p>
                                    {isCustomer && (
                                        <div className="mt-6">
                                            <Button size="sm">
                                                <PlusIcon className="h-4 w-4 mr-2" />
                                                New Application
                                            </Button>
                                        </div>
                                    )}
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    <Card>
                        <CardHeader>
                            <CardTitle>Recent Payments</CardTitle>
                            <CardDescription>
                                Your latest payment transactions
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            {recent_payments.length > 0 ? (
                                <div className="space-y-4">
                                    {recent_payments.slice(0, 5).map((payment: any) => (
                                        <div key={payment.id} className="flex items-center space-x-4">
                                            <div className="flex-shrink-0">
                                                <CreditCardIcon className="h-8 w-8 text-green-500" />
                                            </div>
                                            <div className="flex-1 min-w-0">
                                                <p className="text-sm font-medium truncate">
                                                    MWK {payment.amount?.toLocaleString()}
                                                </p>
                                                <p className="text-sm text-muted-foreground truncate">
                                                    {payment.description}
                                                </p>
                                            </div>
                                            <div className="flex-shrink-0">
                                                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                                    payment.status === 'completed' ? 'bg-green-100 text-green-800' :
                                                    payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                                                    'bg-red-100 text-red-800'
                                                }`}>
                                                    {payment.status}
                                                </span>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="text-center py-6">
                                    <CreditCardIcon className="mx-auto h-12 w-12 text-muted-foreground" />
                                    <h3 className="mt-2 text-sm font-medium">No payments yet</h3>
                                    <p className="mt-1 text-sm text-muted-foreground">
                                        Payment history will appear here once you make payments.
                                    </p>
                                </div>
                            )}
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                {isCustomer && (
                    <Card>
                        <CardHeader>
                            <CardTitle>Quick Actions</CardTitle>
                            <CardDescription>
                                Common tasks you can perform
                            </CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid gap-4 md:grid-cols-3">
                                <Button variant="outline" className="h-20 flex-col gap-2">
                                    <PlusIcon className="h-6 w-6" />
                                    New Application
                                </Button>
                                <Button variant="outline" className="h-20 flex-col gap-2">
                                    <MapPinIcon className="h-6 w-6" />
                                    Track Application
                                </Button>
                                <Button variant="outline" className="h-20 flex-col gap-2">
                                    <CreditCardIcon className="h-6 w-6" />
                                    Make Payment
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
            </div>
        </AppLayout>
    );
}
