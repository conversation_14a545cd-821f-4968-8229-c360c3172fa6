<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | NRWB Specific Services
    |--------------------------------------------------------------------------
    */

    'google_maps' => [
        'api_key' => env('GOOGLE_MAPS_API_KEY'),
        'default_lat' => env('GOOGLE_MAPS_DEFAULT_LAT', -13.9626),
        'default_lng' => env('GOOGLE_MAPS_DEFAULT_LNG', 33.7741),
        'default_zoom' => env('GOOGLE_MAPS_DEFAULT_ZOOM', 10),
    ],

    'ctechpay' => [
        'api_url' => env('CTECHPAY_API_URL', 'https://api.ctechpay.com'),
        'api_key' => env('CTECHPAY_API_KEY'),
        'secret_key' => env('CTECHPAY_SECRET_KEY'),
        'webhook_secret' => env('CTECHPAY_WEBHOOK_SECRET'),
        'environment' => env('CTECHPAY_ENVIRONMENT', 'sandbox'),
    ],

    'mobile_money' => [
        'airtel_enabled' => env('AIRTEL_MONEY_ENABLED', true),
        'tnm_enabled' => env('TNM_MPAMBA_ENABLED', true),
    ],

    'nrwb' => [
        'support_email' => env('NRWB_SUPPORT_EMAIL', '<EMAIL>'),
        'support_phone' => env('NRWB_SUPPORT_PHONE', '+265-1-123-4567'),
        'office_address' => env('NRWB_OFFICE_ADDRESS', 'Northern Region Water Board, Mzuzu, Malawi'),
        'application_fee' => env('APPLICATION_FEE', 5000),
        'connection_fee' => env('CONNECTION_FEE', 25000),
        'currency' => env('CURRENCY', 'MWK'),
    ],

    'file_upload' => [
        'max_size' => env('MAX_FILE_SIZE', 10240), // KB
        'allowed_types' => explode(',', env('ALLOWED_FILE_TYPES', 'pdf,doc,docx,jpg,jpeg,png')),
    ],

];
