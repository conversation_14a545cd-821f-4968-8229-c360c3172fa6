<?php

use App\Http\Controllers\Auth\AuthController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

// Health check endpoint
Route::get('health', function () {
    return response()->json([
        'status' => 'ok',
        'message' => 'NRWB API is running',
        'timestamp' => now()->toISOString(),
        'version' => '1.0.0',
        'database' => \DB::connection()->getPdo() ? 'connected' : 'disconnected',
        'users_count' => \App\Models\User::count(),
        'applications_count' => \App\Models\Application::count(),
        'payments_count' => \App\Models\Payment::count(),
    ]);
});

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('register', [AuthController::class, 'register']);
    Route::post('login', [AuthController::class, 'login']);
});

// Protected authentication routes
Route::middleware('auth')->prefix('auth')->group(function () {
    Route::get('me', [AuthController::class, 'me']);
    Route::post('logout', [AuthController::class, 'logout']);
    Route::post('refresh', [AuthController::class, 'refresh']);
    Route::put('profile', [AuthController::class, 'updateProfile']);
    Route::put('password', [AuthController::class, 'changePassword']);
});

// Application routes
Route::middleware('auth')->prefix('applications')->group(function () {
    // Customer routes
    Route::get('/', [App\Http\Controllers\ApplicationController::class, 'index']);
    Route::post('/', [App\Http\Controllers\ApplicationController::class, 'store']);
    Route::get('/{application}', [App\Http\Controllers\ApplicationController::class, 'show']);
    Route::put('/{application}', [App\Http\Controllers\ApplicationController::class, 'update']);
    Route::post('/{application}/submit', [App\Http\Controllers\ApplicationController::class, 'submit']);

    // Staff routes
    Route::middleware('staff')->prefix('staff')->group(function () {
        Route::get('/', [App\Http\Controllers\Staff\ApplicationController::class, 'index']);
        Route::put('/{application}/status', [App\Http\Controllers\Staff\ApplicationController::class, 'updateStatus']);
        Route::post('/{application}/notes', [App\Http\Controllers\Staff\ApplicationController::class, 'addNotes']);
        Route::get('/statistics', [App\Http\Controllers\Staff\ApplicationController::class, 'statistics']);
        Route::post('/bulk-status', [App\Http\Controllers\Staff\ApplicationController::class, 'bulkUpdateStatus']);
    });
});

// Payment routes
Route::prefix('payments')->group(function () {
    // Public webhook route (no auth required)
    Route::post('webhook', [App\Http\Controllers\PaymentController::class, 'webhook']);

    // Protected payment routes
    Route::middleware('auth')->group(function () {
        Route::get('/', [App\Http\Controllers\PaymentController::class, 'index']);
        Route::post('/', [App\Http\Controllers\PaymentController::class, 'store']);
        Route::get('/{payment}', [App\Http\Controllers\PaymentController::class, 'show']);
        Route::post('/{payment}/check-status', [App\Http\Controllers\PaymentController::class, 'checkStatus']);
        Route::get('/{payment}/receipt', [App\Http\Controllers\PaymentController::class, 'receipt']);
        Route::post('/{payment}/cancel', [App\Http\Controllers\PaymentController::class, 'cancel']);

        // Staff routes
        Route::middleware('staff')->group(function () {
            Route::get('/statistics', [App\Http\Controllers\PaymentController::class, 'statistics']);
        });
    });
});

// User management routes (will be added in next task)
Route::middleware(['auth', 'admin'])->prefix('users')->group(function () {
    // Admin user management routes
});

// Audit log routes (will be added in next task)
Route::middleware(['auth', 'staff'])->prefix('audit-logs')->group(function () {
    // Staff audit log routes
});

// File upload routes (will be added in next task)
Route::middleware('auth')->prefix('files')->group(function () {
    // File upload and management routes
});
