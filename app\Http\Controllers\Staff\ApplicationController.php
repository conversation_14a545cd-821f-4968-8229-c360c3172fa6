<?php

namespace App\Http\Controllers\Staff;

use App\Http\Controllers\Controller;
use App\Models\Application;
use App\Models\ApplicationStatusHistory;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApplicationController extends Controller
{
    /**
     * Get all applications for staff management
     */
    public function index(Request $request)
    {
        $query = Application::with(['user', 'payments', 'documents'])
            ->when($request->status, function ($q, $status) {
                return $q->where('status', $status);
            })
            ->when($request->type, function ($q, $type) {
                return $q->where('type', $type);
            })
            ->when($request->search, function ($q, $search) {
                return $q->where(function ($query) use ($search) {
                    $query->where('application_number', 'like', "%{$search}%")
                          ->orWhere('applicant_name', 'like', "%{$search}%")
                          ->orWhere('property_address', 'like', "%{$search}%")
                          ->orWhereHas('user', function ($userQuery) use ($search) {
                              $userQuery->where('name', 'like', "%{$search}%")
                                       ->orWhere('email', 'like', "%{$search}%");
                          });
                });
            })
            ->when($request->date_from, function ($q, $dateFrom) {
                return $q->whereDate('created_at', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($q, $dateTo) {
                return $q->whereDate('created_at', '<=', $dateTo);
            });

        $applications = $query->orderBy('created_at', 'desc')
                             ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $applications
        ]);
    }

    /**
     * Update application status
     */
    public function updateStatus(Request $request, Application $application)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:under_review,approved,rejected,payment_pending,payment_confirmed,scheduled,in_progress,completed,cancelled',
            'notes' => 'nullable|string|max:1000',
            'rejection_reason' => 'required_if:status,rejected|nullable|string|max:500',
            'scheduled_date' => 'required_if:status,scheduled|nullable|date|after:now',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $oldStatus = $application->status;
        $updateData = [
            'status' => $request->status,
            'staff_notes' => $request->notes,
        ];

        // Set specific timestamps based on status
        switch ($request->status) {
            case Application::STATUS_UNDER_REVIEW:
                $updateData['reviewed_at'] = now();
                break;
            case Application::STATUS_APPROVED:
                $updateData['approved_at'] = now();
                break;
            case Application::STATUS_REJECTED:
                $updateData['rejection_reason'] = $request->rejection_reason;
                break;
            case Application::STATUS_SCHEDULED:
                $updateData['scheduled_date'] = $request->scheduled_date;
                break;
            case Application::STATUS_COMPLETED:
                $updateData['completed_date'] = now();
                break;
        }

        $application->update($updateData);

        // Record status history
        ApplicationStatusHistory::create([
            'application_id' => $application->id,
            'user_id' => $request->user()->id,
            'old_status' => $oldStatus,
            'new_status' => $request->status,
            'notes' => $request->notes
        ]);

        // Log the status change
        AuditLog::logAction(
            AuditLog::ACTION_STATUS_CHANGE,
            $request->user(),
            Application::class,
            $application->id,
            "Application status changed from {$oldStatus} to {$request->status}"
        );

        return response()->json([
            'success' => true,
            'message' => 'Application status updated successfully',
            'data' => $application->fresh()->load(['user', 'payments', 'documents', 'statusHistory.user'])
        ]);
    }

    /**
     * Add staff notes to application
     */
    public function addNotes(Request $request, Application $application)
    {
        $validator = Validator::make($request->all(), [
            'notes' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $oldNotes = $application->staff_notes;
        $newNotes = $oldNotes ? $oldNotes . "\n\n" . now()->format('Y-m-d H:i:s') . " - " . $request->user()->name . ":\n" . $request->notes 
                              : now()->format('Y-m-d H:i:s') . " - " . $request->user()->name . ":\n" . $request->notes;

        $application->update(['staff_notes' => $newNotes]);

        // Log the notes addition
        AuditLog::logAction(
            AuditLog::ACTION_UPDATE,
            $request->user(),
            Application::class,
            $application->id,
            'Staff notes added to application'
        );

        return response()->json([
            'success' => true,
            'message' => 'Notes added successfully',
            'data' => $application->fresh()
        ]);
    }

    /**
     * Get application statistics
     */
    public function statistics(Request $request)
    {
        $stats = [
            'total' => Application::count(),
            'by_status' => Application::selectRaw('status, COUNT(*) as count')
                                    ->groupBy('status')
                                    ->pluck('count', 'status'),
            'by_type' => Application::selectRaw('type, COUNT(*) as count')
                                   ->groupBy('type')
                                   ->pluck('count', 'type'),
            'recent' => Application::where('created_at', '>=', now()->subDays(30))->count(),
            'pending_review' => Application::where('status', Application::STATUS_SUBMITTED)->count(),
            'payment_pending' => Application::where('status', Application::STATUS_PAYMENT_PENDING)->count(),
            'in_progress' => Application::where('status', Application::STATUS_IN_PROGRESS)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats
        ]);
    }

    /**
     * Bulk update application statuses
     */
    public function bulkUpdateStatus(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'application_ids' => 'required|array|min:1',
            'application_ids.*' => 'exists:applications,id',
            'status' => 'required|in:under_review,approved,rejected,payment_pending,cancelled',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $applications = Application::whereIn('id', $request->application_ids)->get();
        $updated = 0;

        foreach ($applications as $application) {
            $oldStatus = $application->status;
            
            $application->update([
                'status' => $request->status,
                'staff_notes' => $request->notes,
            ]);

            // Record status history
            ApplicationStatusHistory::create([
                'application_id' => $application->id,
                'user_id' => $request->user()->id,
                'old_status' => $oldStatus,
                'new_status' => $request->status,
                'notes' => $request->notes . ' (Bulk update)'
            ]);

            // Log the status change
            AuditLog::logAction(
                AuditLog::ACTION_STATUS_CHANGE,
                $request->user(),
                Application::class,
                $application->id,
                "Bulk status change from {$oldStatus} to {$request->status}"
            );

            $updated++;
        }

        return response()->json([
            'success' => true,
            'message' => "Successfully updated {$updated} applications",
            'data' => ['updated_count' => $updated]
        ]);
    }
}
