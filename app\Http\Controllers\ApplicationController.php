<?php

namespace App\Http\Controllers;

use App\Models\Application;
use App\Models\ApplicationStatusHistory;
use App\Models\AuditLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ApplicationController extends Controller
{
    /**
     * Get applications for the authenticated user
     */
    public function index(Request $request)
    {
        $user = $request->user();
        
        $query = Application::with(['user', 'payments', 'documents'])
            ->when($user->isCustomer(), function ($q) use ($user) {
                return $q->where('user_id', $user->id);
            })
            ->when($request->status, function ($q, $status) {
                return $q->where('status', $status);
            })
            ->when($request->type, function ($q, $type) {
                return $q->where('type', $type);
            })
            ->when($request->search, function ($q, $search) {
                return $q->where(function ($query) use ($search) {
                    $query->where('application_number', 'like', "%{$search}%")
                          ->orWhere('applicant_name', 'like', "%{$search}%")
                          ->orWhere('property_address', 'like', "%{$search}%");
                });
            });

        $applications = $query->orderBy('created_at', 'desc')
                             ->paginate($request->per_page ?? 15);

        return response()->json([
            'success' => true,
            'data' => $applications
        ]);
    }

    /**
     * Create a new application
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'type' => 'required|in:new_connection,reconnection,upgrade,transfer',
            'property_address' => 'required|string|max:500',
            'property_latitude' => 'nullable|numeric|between:-90,90',
            'property_longitude' => 'nullable|numeric|between:-180,180',
            'property_description' => 'nullable|string|max:1000',
            'connection_type' => 'nullable|string|max:100',
            'meter_size' => 'nullable|string|max:50',
            'estimated_usage' => 'nullable|integer|min:0',
            'purpose_of_use' => 'nullable|string|max:200',
            'applicant_name' => 'required|string|max:255',
            'applicant_phone' => 'required|string|max:20',
            'applicant_email' => 'nullable|email|max:255',
            'applicant_national_id' => 'nullable|string|max:50',
            'property_owner_name' => 'nullable|string|max:255',
            'property_owner_phone' => 'nullable|string|max:20',
            'property_owner_national_id' => 'nullable|string|max:50',
            'is_property_owner' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $applicationFee = config('services.nrwb.application_fee', 5000);
        $connectionFee = config('services.nrwb.connection_fee', 25000);

        $application = Application::create([
            'user_id' => $request->user()->id,
            'type' => $request->type,
            'status' => Application::STATUS_DRAFT,
            'property_address' => $request->property_address,
            'property_latitude' => $request->property_latitude,
            'property_longitude' => $request->property_longitude,
            'property_description' => $request->property_description,
            'connection_type' => $request->connection_type,
            'meter_size' => $request->meter_size,
            'estimated_usage' => $request->estimated_usage,
            'purpose_of_use' => $request->purpose_of_use,
            'applicant_name' => $request->applicant_name,
            'applicant_phone' => $request->applicant_phone,
            'applicant_email' => $request->applicant_email,
            'applicant_national_id' => $request->applicant_national_id,
            'property_owner_name' => $request->property_owner_name,
            'property_owner_phone' => $request->property_owner_phone,
            'property_owner_national_id' => $request->property_owner_national_id,
            'is_property_owner' => $request->is_property_owner ?? false,
            'application_fee' => $applicationFee,
            'connection_fee' => $connectionFee,
            'total_amount' => $applicationFee + $connectionFee,
            'notes' => $request->notes,
        ]);

        // Log the creation
        AuditLog::logAction(
            AuditLog::ACTION_CREATE,
            $request->user(),
            Application::class,
            $application->id,
            'Application created'
        );

        return response()->json([
            'success' => true,
            'message' => 'Application created successfully',
            'data' => $application->load(['user', 'payments', 'documents'])
        ], 201);
    }

    /**
     * Get a specific application
     */
    public function show(Request $request, Application $application)
    {
        $user = $request->user();

        // Check if user can view this application
        if ($user->isCustomer() && $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view this application'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $application->load([
                'user', 
                'payments', 
                'documents.uploadedBy', 
                'statusHistory.user'
            ])
        ]);
    }

    /**
     * Update an application
     */
    public function update(Request $request, Application $application)
    {
        $user = $request->user();

        // Check if user can update this application
        if ($user->isCustomer() && $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this application'
            ], 403);
        }

        // Check if application can be edited
        if (!$application->canBeEdited()) {
            return response()->json([
                'success' => false,
                'message' => 'Application cannot be edited in its current status'
            ], 422);
        }

        $validator = Validator::make($request->all(), [
            'property_address' => 'required|string|max:500',
            'property_latitude' => 'nullable|numeric|between:-90,90',
            'property_longitude' => 'nullable|numeric|between:-180,180',
            'property_description' => 'nullable|string|max:1000',
            'connection_type' => 'nullable|string|max:100',
            'meter_size' => 'nullable|string|max:50',
            'estimated_usage' => 'nullable|integer|min:0',
            'purpose_of_use' => 'nullable|string|max:200',
            'applicant_name' => 'required|string|max:255',
            'applicant_phone' => 'required|string|max:20',
            'applicant_email' => 'nullable|email|max:255',
            'applicant_national_id' => 'nullable|string|max:50',
            'property_owner_name' => 'nullable|string|max:255',
            'property_owner_phone' => 'nullable|string|max:20',
            'property_owner_national_id' => 'nullable|string|max:50',
            'is_property_owner' => 'boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $oldValues = $application->toArray();

        $application->update($request->only([
            'property_address', 'property_latitude', 'property_longitude',
            'property_description', 'connection_type', 'meter_size',
            'estimated_usage', 'purpose_of_use', 'applicant_name',
            'applicant_phone', 'applicant_email', 'applicant_national_id',
            'property_owner_name', 'property_owner_phone', 'property_owner_national_id',
            'is_property_owner', 'notes'
        ]));

        // Log the update
        AuditLog::logAction(
            AuditLog::ACTION_UPDATE,
            $user,
            Application::class,
            $application->id,
            'Application updated',
            $oldValues,
            $application->fresh()->toArray()
        );

        return response()->json([
            'success' => true,
            'message' => 'Application updated successfully',
            'data' => $application->fresh()->load(['user', 'payments', 'documents'])
        ]);
    }

    /**
     * Submit an application
     */
    public function submit(Request $request, Application $application)
    {
        $user = $request->user();

        // Check if user can submit this application
        if ($user->isCustomer() && $application->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to submit this application'
            ], 403);
        }

        if ($application->status !== Application::STATUS_DRAFT) {
            return response()->json([
                'success' => false,
                'message' => 'Only draft applications can be submitted'
            ], 422);
        }

        $oldStatus = $application->status;
        $application->update([
            'status' => Application::STATUS_SUBMITTED,
            'submitted_at' => now()
        ]);

        // Record status history
        ApplicationStatusHistory::create([
            'application_id' => $application->id,
            'user_id' => $user->id,
            'old_status' => $oldStatus,
            'new_status' => Application::STATUS_SUBMITTED,
            'notes' => 'Application submitted by customer'
        ]);

        // Log the submission
        AuditLog::logAction(
            AuditLog::ACTION_STATUS_CHANGE,
            $user,
            Application::class,
            $application->id,
            'Application submitted'
        );

        return response()->json([
            'success' => true,
            'message' => 'Application submitted successfully',
            'data' => $application->fresh()->load(['user', 'payments', 'documents'])
        ]);
    }
}
