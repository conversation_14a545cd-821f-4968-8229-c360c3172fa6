[2025-09-06 15:12:29] local.ERROR: Trait "Laravel\Sanctum\HasApiTokens" not found {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Trait \"Laravel\\Sanctum\\HasApiTokens\" not found at C:\\Users\\<USER>\\app\\app\\Models\\User.php:12)
[stacktrace]
#0 {main}
"} 
[2025-09-06 15:16:43] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email (Connection: sqlite, SQL: insert into "users" ("name", "email", "password", "phone", "role", "address", "national_id", "is_active", "email_verified_at", "updated_at", "created_at") values (NRWB Administrator, <EMAIL>, $2y$12$P/DDa5Tn2t9rZUdKNbEhSOB00K2Bba5J4IEUmV1g2vj8xcFw5spP6, +265-1-123-4567, admin, NRWB Head Office, Mzuzu, ADM001, 1, 2025-09-06 15:16:43, 2025-09-06 15:16:43, 2025-09-06 15:16:43)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email (Connection: sqlite, SQL: insert into \"users\" (\"name\", \"email\", \"password\", \"phone\", \"role\", \"address\", \"national_id\", \"is_active\", \"email_verified_at\", \"updated_at\", \"created_at\") values (NRWB Administrator, <EMAIL>, $2y$12$P/DDa5Tn2t9rZUdKNbEhSOB00K2Bba5J4IEUmV1g2vj8xcFw5spP6, +265-1-123-4567, admin, NRWB Head Office, Mzuzu, ADM001, 1, 2025-09-06 15:16:43, 2025-09-06 15:16:43, 2025-09-06 15:16:43)) at C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:819)
[stacktrace]
#0 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#3 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#4 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#5 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#6 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#7 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#8 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1204): Illuminate\\Database\\Eloquent\\Model->save()
#10 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#11 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1203): tap(Object(App\\Models\\User), Object(Closure))
#12 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#13 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#14 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#15 C:\\Users\\<USER>\\app\\database\\seeders\\DatabaseSeeder.php(19): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#16 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#17 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#18 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#19 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#20 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#21 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#22 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#23 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#24 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#25 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#26 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#27 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#32 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#33 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#34 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#40 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 19 UNIQUE constraint failed: users.email at C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:570)
[stacktrace]
#0 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(570): PDOStatement->execute()
#1 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(811): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('insert into \"us...', Array)
#2 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(778): Illuminate\\Database\\Connection->runQueryCallback('insert into \"us...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(559): Illuminate\\Database\\Connection->run('insert into \"us...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(523): Illuminate\\Database\\Connection->statement('insert into \"us...', Array)
#5 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\Processor.php(32): Illuminate\\Database\\Connection->insert('insert into \"us...', Array)
#6 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3853): Illuminate\\Database\\Query\\Processors\\Processor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into \"us...', Array, 'id')
#7 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2220): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#8 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1436): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#9 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1401): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#10 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1240): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#11 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1204): Illuminate\\Database\\Eloquent\\Model->save()
#12 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(390): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\User))
#13 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1203): tap(Object(App\\Models\\User), Object(Closure))
#14 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#15 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2540): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#16 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2556): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#17 C:\\Users\\<USER>\\app\\database\\seeders\\DatabaseSeeder.php(19): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#18 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#19 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#20 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#21 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#22 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#23 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(188): Illuminate\\Container\\Container->call(Array, Array)
#24 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(197): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#25 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(71): Illuminate\\Database\\Seeder->__invoke()
#26 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(157): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#27 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#28 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#29 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(43): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#30 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(96): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#31 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#32 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(836): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#33 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#34 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Command\\Command.php(318): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#35 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(1110): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#37 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(359): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 C:\\Users\\<USER>\\app\\vendor\\symfony\\console\\Application.php(194): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(197): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 C:\\Users\\<USER>\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 C:\\Users\\<USER>\\app\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#42 {main}
"} 
