<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Payment extends Model
{
    use HasFactory;

    // Payment statuses
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_REFUNDED = 'refunded';

    // Payment methods
    const METHOD_AIRTEL_MONEY = 'airtel_money';
    const METHOD_TNM_MPAMBA = 'tnm_mpamba';
    const METHOD_BANK_TRANSFER = 'bank_transfer';
    const METHOD_CASH = 'cash';

    // Payment types
    const TYPE_APPLICATION_FEE = 'application_fee';
    const TYPE_CONNECTION_FEE = 'connection_fee';
    const TYPE_RECONNECTION_FEE = 'reconnection_fee';
    const TYPE_UPGRADE_FEE = 'upgrade_fee';

    protected $fillable = [
        'user_id',
        'application_id',
        'payment_reference',
        'external_reference',
        'amount',
        'currency',
        'payment_method',
        'payment_type',
        'status',
        'phone_number',
        'payer_name',
        'description',
        'gateway_response',
        'failure_reason',
        'processed_at',
        'failed_at',
        'refunded_at',
        'webhook_data',
    ];

    protected function casts(): array
    {
        return [
            'amount' => 'decimal:2',
            'gateway_response' => 'array',
            'webhook_data' => 'array',
            'processed_at' => 'datetime',
            'failed_at' => 'datetime',
            'refunded_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the payment
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the application this payment is for
     */
    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    /**
     * Generate payment reference
     */
    public static function generatePaymentReference(): string
    {
        return 'PAY-' . strtoupper(uniqid()) . '-' . time();
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_PENDING => 'Pending',
            self::STATUS_PROCESSING => 'Processing',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_FAILED => 'Failed',
            self::STATUS_CANCELLED => 'Cancelled',
            self::STATUS_REFUNDED => 'Refunded',
        ];
    }

    /**
     * Get available payment methods
     */
    public static function getPaymentMethods(): array
    {
        return [
            self::METHOD_AIRTEL_MONEY => 'Airtel Money',
            self::METHOD_TNM_MPAMBA => 'TNM Mpamba',
            self::METHOD_BANK_TRANSFER => 'Bank Transfer',
            self::METHOD_CASH => 'Cash',
        ];
    }

    /**
     * Get available payment types
     */
    public static function getPaymentTypes(): array
    {
        return [
            self::TYPE_APPLICATION_FEE => 'Application Fee',
            self::TYPE_CONNECTION_FEE => 'Connection Fee',
            self::TYPE_RECONNECTION_FEE => 'Reconnection Fee',
            self::TYPE_UPGRADE_FEE => 'Upgrade Fee',
        ];
    }

    /**
     * Check if payment is successful
     */
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * Check if payment is pending
     */
    public function isPending(): bool
    {
        return in_array($this->status, [self::STATUS_PENDING, self::STATUS_PROCESSING]);
    }

    /**
     * Check if payment has failed
     */
    public function hasFailed(): bool
    {
        return in_array($this->status, [self::STATUS_FAILED, self::STATUS_CANCELLED]);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'yellow',
            self::STATUS_PROCESSING => 'blue',
            self::STATUS_COMPLETED => 'green',
            self::STATUS_FAILED => 'red',
            self::STATUS_CANCELLED => 'gray',
            self::STATUS_REFUNDED => 'orange',
            default => 'gray',
        };
    }

    /**
     * Format amount with currency
     */
    public function getFormattedAmount(): string
    {
        return number_format($this->amount, 2) . ' ' . $this->currency;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($payment) {
            if (empty($payment->payment_reference)) {
                $payment->payment_reference = self::generatePaymentReference();
            }
            if (empty($payment->currency)) {
                $payment->currency = config('services.nrwb.currency', 'MWK');
            }
        });
    }
}
