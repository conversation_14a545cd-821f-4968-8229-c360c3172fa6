<?php

namespace Database\Seeders;

use App\Models\Application;
use App\Models\Payment;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create default users with different roles
        $admin = User::create([
            'name' => 'NRWB Administrator',
            'email' => '<EMAIL>',
            'password' => Hash::make('Admin123!'),
            'phone' => '+265-1-123-4567',
            'role' => User::ROLE_ADMIN,
            'address' => 'NRWB Head Office, Mzuzu',
            'national_id' => 'ADM001',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $management = User::create([
            'name' => 'NRWB Management',
            'email' => '<EMAIL>',
            'password' => Hash::make('Management123!'),
            'phone' => '+265-1-123-4568',
            'role' => User::ROLE_MANAGEMENT,
            'address' => 'NRWB Head Office, Mzuzu',
            'national_id' => 'MGT001',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $staff = User::create([
            'name' => 'NRWB Staff Member',
            'email' => '<EMAIL>',
            'password' => Hash::make('Staff123!'),
            'phone' => '+265-1-123-4569',
            'role' => User::ROLE_STAFF,
            'address' => 'NRWB Office, Mzuzu',
            'national_id' => 'STF001',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $customer1 = User::create([
            'name' => 'John Banda',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer123!'),
            'phone' => '+265-999-123-456',
            'role' => User::ROLE_CUSTOMER,
            'address' => 'Area 10, Lilongwe',
            'national_id' => 'MWI123456789',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $customer2 = User::create([
            'name' => 'Mary Phiri',
            'email' => '<EMAIL>',
            'password' => Hash::make('Customer123!'),
            'phone' => '+265-888-987-654',
            'role' => User::ROLE_CUSTOMER,
            'address' => 'Mzuzu City',
            'national_id' => 'MWI987654321',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create sample applications
        $application1 = Application::create([
            'user_id' => $customer1->id,
            'type' => Application::TYPE_NEW_CONNECTION,
            'status' => Application::STATUS_SUBMITTED,
            'property_address' => 'Plot 123, Area 10, Lilongwe',
            'property_latitude' => -13.9626,
            'property_longitude' => 33.7741,
            'property_description' => 'Residential property with 3 bedrooms',
            'connection_type' => 'Domestic',
            'meter_size' => '15mm',
            'estimated_usage' => 50,
            'purpose_of_use' => 'Residential',
            'applicant_name' => 'John Banda',
            'applicant_phone' => '+265-999-123-456',
            'applicant_email' => '<EMAIL>',
            'applicant_national_id' => 'MWI123456789',
            'is_property_owner' => true,
            'application_fee' => 5000,
            'connection_fee' => 25000,
            'total_amount' => 30000,
            'notes' => 'Urgent connection required for new family home',
            'submitted_at' => now()->subDays(5),
        ]);

        $application2 = Application::create([
            'user_id' => $customer2->id,
            'type' => Application::TYPE_NEW_CONNECTION,
            'status' => Application::STATUS_PAYMENT_PENDING,
            'property_address' => 'Mzuzu City Center, Shop 45',
            'property_latitude' => -11.4607,
            'property_longitude' => 34.0142,
            'property_description' => 'Commercial shop for retail business',
            'connection_type' => 'Commercial',
            'meter_size' => '20mm',
            'estimated_usage' => 100,
            'purpose_of_use' => 'Commercial',
            'applicant_name' => 'Mary Phiri',
            'applicant_phone' => '+265-888-987-654',
            'applicant_email' => '<EMAIL>',
            'applicant_national_id' => 'MWI987654321',
            'is_property_owner' => true,
            'application_fee' => 5000,
            'connection_fee' => 35000,
            'total_amount' => 40000,
            'notes' => 'New shop opening next month',
            'submitted_at' => now()->subDays(3),
            'reviewed_at' => now()->subDays(2),
            'approved_at' => now()->subDays(1),
        ]);

        $application3 = Application::create([
            'user_id' => $customer1->id,
            'type' => Application::TYPE_RECONNECTION,
            'status' => Application::STATUS_COMPLETED,
            'property_address' => 'Plot 456, Area 12, Lilongwe',
            'property_latitude' => -13.9800,
            'property_longitude' => 33.7900,
            'property_description' => 'Reconnection after payment of arrears',
            'connection_type' => 'Domestic',
            'meter_size' => '15mm',
            'estimated_usage' => 40,
            'purpose_of_use' => 'Residential',
            'applicant_name' => 'John Banda',
            'applicant_phone' => '+265-999-123-456',
            'applicant_email' => '<EMAIL>',
            'applicant_national_id' => 'MWI123456789',
            'is_property_owner' => true,
            'application_fee' => 2500,
            'connection_fee' => 15000,
            'total_amount' => 17500,
            'notes' => 'Reconnection after clearing outstanding balance',
            'submitted_at' => now()->subDays(10),
            'reviewed_at' => now()->subDays(9),
            'approved_at' => now()->subDays(8),
            'scheduled_date' => now()->subDays(7),
            'completed_date' => now()->subDays(6),
        ]);

        // Create sample payments
        Payment::create([
            'user_id' => $customer1->id,
            'application_id' => $application3->id,
            'amount' => 17500,
            'payment_method' => Payment::METHOD_AIRTEL_MONEY,
            'payment_type' => Payment::TYPE_RECONNECTION_FEE,
            'status' => Payment::STATUS_COMPLETED,
            'phone_number' => '+265-999-123-456',
            'payer_name' => 'John Banda',
            'description' => 'Reconnection fee payment',
            'processed_at' => now()->subDays(7),
        ]);

        Payment::create([
            'user_id' => $customer2->id,
            'application_id' => $application2->id,
            'amount' => 5000,
            'payment_method' => Payment::METHOD_TNM_MPAMBA,
            'payment_type' => Payment::TYPE_APPLICATION_FEE,
            'status' => Payment::STATUS_PENDING,
            'phone_number' => '+265-888-987-654',
            'payer_name' => 'Mary Phiri',
            'description' => 'Application fee payment',
        ]);

        $this->command->info('NRWB database seeded successfully!');
        $this->command->info('Default login credentials:');
        $this->command->info('Admin: <EMAIL> / Admin123!');
        $this->command->info('Management: <EMAIL> / Management123!');
        $this->command->info('Staff: <EMAIL> / Staff123!');
        $this->command->info('Customer: <EMAIL> / Customer123!');
    }
}
