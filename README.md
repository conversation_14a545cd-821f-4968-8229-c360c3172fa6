# Northern Region Water Board (NRWB) - Laravel Application

## 📋 Project Overview

The Northern Region Water Board (NRWB) is a comprehensive water management system built with Laravel, Inertia.js, and React. The system manages water connection applications, payments, user management, and GIS monitoring for the Northern Region Water Board in Malawi.

## 🏗️ Architecture

### Backend (Lara<PERSON> 11)
- **Framework**: Laravel 11 with MySQL/SQLite
- **Authentication**: Laravel Sanctum with role-based access control
- **Payment Integration**: CTechPay API for mobile money payments
- **API**: RESTful API with comprehensive endpoints

### Frontend (React/Inertia.js)
- **Framework**: React 19 with Inertia.js
- **Styling**: Tailwind CSS 4 with Radix UI components
- **State Management**: Inertia.js for server state
- **Maps**: Google Maps API integration
- **Build Tool**: Vite

## 🎯 Core Features

### 1. User Management & Authentication
- **Role-based Access Control**: Customer, Staff, Admin, Management
- **Laravel Sanctum Authentication** with API tokens
- **Password hashing** with bcrypt
- **User profile management**
- **Email verification**

### 2. Application Management
- **Water Connection Applications**: Complete application workflow
- **Status Tracking**: Real-time application status updates
- **Document Management**: File uploads and attachments
- **GIS Integration**: Location mapping for properties
- **Application Types**: New connection, reconnection, upgrade, transfer

### 3. Payment Processing
- **Mobile Money Integration**: CTechPay API (Airtel Money, TNM Mpamba)
- **Payment Tracking**: Complete payment history and status
- **Receipt Generation**: Digital receipt downloads
- **Payment Webhooks**: Real-time payment status updates
- **Multiple Payment Types**: Application fee, connection fee, etc.

### 4. GIS & Mapping
- **Google Maps Integration**: Property location mapping
- **Location Picker**: Interactive map for selecting property locations
- **Coordinate Storage**: Precise latitude/longitude tracking

### 5. Admin & Management
- **User Management**: Create, update, delete users
- **Application Management**: Review, approve, reject applications
- **Payment Monitoring**: Track all payment transactions
- **Audit Logging**: Complete activity tracking
- **Statistics Dashboard**: Comprehensive reporting

## 📁 Project Structure

```
app/
├── Http/
│   ├── Controllers/
│   │   ├── Auth/AuthController.php          # Authentication endpoints
│   │   ├── ApplicationController.php        # Customer application management
│   │   ├── PaymentController.php           # Payment processing
│   │   └── Staff/ApplicationController.php  # Staff application management
│   └── Middleware/
│       ├── RoleMiddleware.php              # Role-based access control
│       ├── StaffMiddleware.php             # Staff privilege middleware
│       └── AdminMiddleware.php             # Admin privilege middleware
├── Models/
│   ├── User.php                           # User model with roles
│   ├── Application.php                    # Water connection applications
│   ├── Payment.php                        # Payment transactions
│   ├── ApplicationDocument.php            # Document attachments
│   ├── ApplicationStatusHistory.php       # Status change tracking
│   └── AuditLog.php                      # System audit logging
├── Services/
│   └── PaymentService.php                # CTechPay integration service
database/
├── migrations/                           # Database schema migrations
└── seeders/DatabaseSeeder.php           # Sample data seeder
resources/
├── js/
│   ├── components/                       # Reusable React components
│   ├── pages/                           # Page components
│   │   ├── auth/                        # Authentication pages
│   │   └── dashboard.tsx                # Main dashboard
│   ├── layouts/                         # Layout components
│   └── types/                          # TypeScript type definitions
└── views/                              # Blade templates (minimal)
routes/
├── api.php                             # API routes
└── web.php                             # Web routes
```

## 🔐 User Roles & Permissions

### Customer
- Create water connection applications
- View application status and history
- Make payments via mobile money
- Upload required documents
- Manage profile information

### Staff
- View and manage all applications
- Update application statuses
- Add staff notes to applications
- Process payments manually
- Access basic reporting

### Admin
- All staff permissions
- User management (create, update, deactivate users)
- System administration
- Access audit logs
- Advanced reporting and statistics

### Management
- All admin permissions
- Strategic oversight and analytics
- System-wide reporting
- Policy and configuration management

## 🛠️ Technology Stack

### Backend Dependencies
- **Laravel 11**: PHP framework
- **Laravel Sanctum**: API authentication
- **MySQL/SQLite**: Database
- **Guzzle HTTP**: HTTP client for API calls

### Frontend Dependencies
- **React 19**: UI framework
- **Inertia.js**: Server-side rendering
- **Tailwind CSS 4**: Utility-first CSS
- **Radix UI**: Accessible component primitives
- **Lucide React**: Icon library
- **Google Maps API**: Location services

## 🚀 Getting Started

### Prerequisites
- PHP 8.2+
- Composer
- Node.js 18+
- MySQL 8.0+ or SQLite
- Google Maps API Key
- CTechPay API credentials (for payments)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd nrwb-laravel
   ```

2. **Install PHP dependencies**
   ```bash
   composer install
   ```

3. **Install Node.js dependencies**
   ```bash
   npm install
   ```

4. **Environment setup**
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```

5. **Configure environment variables**
   Edit `.env` file with your database, Google Maps, and CTechPay credentials:
   ```env
   DB_CONNECTION=mysql
   DB_DATABASE=nrwb_db
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   
   GOOGLE_MAPS_API_KEY=your_google_maps_key
   CTECHPAY_API_KEY=your_ctechpay_key
   CTECHPAY_SECRET_KEY=your_ctechpay_secret
   ```

6. **Database setup**
   ```bash
   php artisan migrate
   php artisan db:seed
   ```

7. **Build frontend assets**
   ```bash
   npm run build
   ```

8. **Start the development server**
   ```bash
   php artisan serve
   npm run dev
   ```

The application will be available at `http://localhost:8000`

## 🔧 Configuration

### Environment Variables

Key environment variables for NRWB:

```env
# Application
APP_NAME="Northern Region Water Board"
APP_URL=http://localhost:8000

# Database
DB_CONNECTION=mysql
DB_DATABASE=nrwb_db

# Google Maps
GOOGLE_MAPS_API_KEY=your_api_key
GOOGLE_MAPS_DEFAULT_LAT=-13.9626
GOOGLE_MAPS_DEFAULT_LNG=33.7741

# CTechPay Payment Gateway
CTECHPAY_API_URL=https://api.ctechpay.com
CTECHPAY_API_KEY=your_api_key
CTECHPAY_SECRET_KEY=your_secret_key
CTECHPAY_ENVIRONMENT=sandbox

# NRWB Settings
APPLICATION_FEE=5000
CONNECTION_FEE=25000
CURRENCY=MWK
```

## 🧪 Default Login Credentials

After running the seeder, use these credentials to test the system:

- **Admin**: <EMAIL> / Admin123!
- **Management**: <EMAIL> / Management123!
- **Staff**: <EMAIL> / Staff123!
- **Customer**: <EMAIL> / Customer123!

## 📊 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - User logout

### Applications
- `GET /api/applications` - Get user applications
- `POST /api/applications` - Create new application
- `GET /api/applications/{id}` - Get specific application
- `PUT /api/applications/{id}` - Update application
- `POST /api/applications/{id}/submit` - Submit application

### Staff Application Management
- `GET /api/applications/staff` - Get all applications (staff)
- `PUT /api/applications/staff/{id}/status` - Update application status
- `POST /api/applications/staff/{id}/notes` - Add staff notes

### Payments
- `GET /api/payments` - Get user payments
- `POST /api/payments` - Process new payment
- `GET /api/payments/{id}` - Get specific payment
- `POST /api/payments/webhook` - Payment webhook (public)

## 🔍 Key Features in Detail

### Application Workflow
1. Customer creates application with property details
2. Customer uploads required documents
3. Customer submits application for review
4. Staff reviews and updates status
5. If approved, customer makes payment
6. Staff schedules connection work
7. Connection is completed and activated

### Payment Integration
- Supports Airtel Money and TNM Mpamba
- Real-time payment status updates via webhooks
- Automatic application status updates on successful payment
- Digital receipt generation and download

### Security Features
- Role-based access control with middleware
- API authentication with Laravel Sanctum
- Input validation and sanitization
- Comprehensive audit logging
- Secure file upload handling

## 📈 Performance & Monitoring

- Database indexing for optimal query performance
- Eager loading to prevent N+1 queries
- Comprehensive audit logging for system monitoring
- Error logging and exception handling

## 🐛 Known Limitations

1. **File Upload**: Limited to specific file types and sizes
2. **Offline Support**: No offline functionality
3. **Mobile App**: Web-only, no native mobile app
4. **Bulk Operations**: Limited bulk processing capabilities

## 🔄 Development

### Running Tests
```bash
php artisan test
```

### Code Style
```bash
npm run lint
npm run format
```

### Database Reset
```bash
php artisan migrate:fresh --seed
```

This comprehensive Laravel application provides a robust foundation for managing water connection applications, payments, and user management for the Northern Region Water Board in Malawi.
