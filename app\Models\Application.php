<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Application extends Model
{
    use HasFactory, SoftDeletes;

    // Application statuses
    const STATUS_DRAFT = 'draft';
    const STATUS_SUBMITTED = 'submitted';
    const STATUS_UNDER_REVIEW = 'under_review';
    const STATUS_APPROVED = 'approved';
    const STATUS_REJECTED = 'rejected';
    const STATUS_PAYMENT_PENDING = 'payment_pending';
    const STATUS_PAYMENT_CONFIRMED = 'payment_confirmed';
    const STATUS_SCHEDULED = 'scheduled';
    const STATUS_IN_PROGRESS = 'in_progress';
    const STATUS_COMPLETED = 'completed';
    const STATUS_CANCELLED = 'cancelled';

    // Application types
    const TYPE_NEW_CONNECTION = 'new_connection';
    const TYPE_RECONNECTION = 'reconnection';
    const TYPE_UPGRADE = 'upgrade';
    const TYPE_TRANSFER = 'transfer';

    protected $fillable = [
        'user_id',
        'application_number',
        'type',
        'status',
        'property_address',
        'property_latitude',
        'property_longitude',
        'property_description',
        'connection_type',
        'meter_size',
        'estimated_usage',
        'purpose_of_use',
        'applicant_name',
        'applicant_phone',
        'applicant_email',
        'applicant_national_id',
        'property_owner_name',
        'property_owner_phone',
        'property_owner_national_id',
        'is_property_owner',
        'application_fee',
        'connection_fee',
        'total_amount',
        'notes',
        'staff_notes',
        'rejection_reason',
        'scheduled_date',
        'completed_date',
        'submitted_at',
        'reviewed_at',
        'approved_at',
    ];

    protected function casts(): array
    {
        return [
            'is_property_owner' => 'boolean',
            'application_fee' => 'decimal:2',
            'connection_fee' => 'decimal:2',
            'total_amount' => 'decimal:2',
            'property_latitude' => 'decimal:8',
            'property_longitude' => 'decimal:8',
            'estimated_usage' => 'integer',
            'submitted_at' => 'datetime',
            'reviewed_at' => 'datetime',
            'approved_at' => 'datetime',
            'scheduled_date' => 'datetime',
            'completed_date' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the application
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get all payments for this application
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get all documents for this application
     */
    public function documents(): HasMany
    {
        return $this->hasMany(ApplicationDocument::class);
    }

    /**
     * Get all status history for this application
     */
    public function statusHistory(): HasMany
    {
        return $this->hasMany(ApplicationStatusHistory::class);
    }

    /**
     * Generate application number
     */
    public static function generateApplicationNumber(): string
    {
        $year = date('Y');
        $month = date('m');
        $count = self::whereYear('created_at', $year)
                    ->whereMonth('created_at', $month)
                    ->count() + 1;
        
        return sprintf('NRWB-%s%s-%04d', $year, $month, $count);
    }

    /**
     * Get available statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_SUBMITTED => 'Submitted',
            self::STATUS_UNDER_REVIEW => 'Under Review',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_PAYMENT_PENDING => 'Payment Pending',
            self::STATUS_PAYMENT_CONFIRMED => 'Payment Confirmed',
            self::STATUS_SCHEDULED => 'Scheduled',
            self::STATUS_IN_PROGRESS => 'In Progress',
            self::STATUS_COMPLETED => 'Completed',
            self::STATUS_CANCELLED => 'Cancelled',
        ];
    }

    /**
     * Get available types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_NEW_CONNECTION => 'New Connection',
            self::TYPE_RECONNECTION => 'Reconnection',
            self::TYPE_UPGRADE => 'Upgrade',
            self::TYPE_TRANSFER => 'Transfer',
        ];
    }

    /**
     * Check if application can be edited
     */
    public function canBeEdited(): bool
    {
        return in_array($this->status, [self::STATUS_DRAFT, self::STATUS_REJECTED]);
    }

    /**
     * Check if application can be cancelled
     */
    public function canBeCancelled(): bool
    {
        return !in_array($this->status, [self::STATUS_COMPLETED, self::STATUS_CANCELLED]);
    }

    /**
     * Check if payment is required
     */
    public function requiresPayment(): bool
    {
        return $this->status === self::STATUS_PAYMENT_PENDING;
    }

    /**
     * Get status color for UI
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_DRAFT => 'gray',
            self::STATUS_SUBMITTED => 'blue',
            self::STATUS_UNDER_REVIEW => 'yellow',
            self::STATUS_APPROVED => 'green',
            self::STATUS_REJECTED => 'red',
            self::STATUS_PAYMENT_PENDING => 'orange',
            self::STATUS_PAYMENT_CONFIRMED => 'green',
            self::STATUS_SCHEDULED => 'purple',
            self::STATUS_IN_PROGRESS => 'blue',
            self::STATUS_COMPLETED => 'green',
            self::STATUS_CANCELLED => 'red',
            default => 'gray',
        };
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($application) {
            if (empty($application->application_number)) {
                $application->application_number = self::generateApplicationNumber();
            }
        });
    }
}
