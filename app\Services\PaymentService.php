<?php

namespace App\Services;

use App\Models\Application;
use App\Models\Payment;
use App\Models\AuditLog;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PaymentService
{
    protected $apiUrl;
    protected $apiKey;
    protected $secretKey;
    protected $environment;

    public function __construct()
    {
        $this->apiUrl = config('services.ctechpay.api_url');
        $this->apiKey = config('services.ctechpay.api_key');
        $this->secretKey = config('services.ctechpay.secret_key');
        $this->environment = config('services.ctechpay.environment');
    }

    /**
     * Process a mobile money payment
     */
    public function processPayment(Application $application, array $paymentData)
    {
        try {
            // Create payment record
            $payment = Payment::create([
                'user_id' => $application->user_id,
                'application_id' => $application->id,
                'amount' => $paymentData['amount'],
                'payment_method' => $paymentData['payment_method'],
                'payment_type' => $paymentData['payment_type'],
                'phone_number' => $paymentData['phone_number'],
                'payer_name' => $paymentData['payer_name'] ?? null,
                'description' => $paymentData['description'] ?? "Payment for application {$application->application_number}",
                'status' => Payment::STATUS_PENDING,
            ]);

            // Prepare CTechPay request
            $requestData = [
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'phone_number' => $payment->phone_number,
                'payment_method' => $this->mapPaymentMethod($payment->payment_method),
                'reference' => $payment->payment_reference,
                'description' => $payment->description,
                'callback_url' => url('/api/payments/webhook'),
                'return_url' => config('app.frontend_url') . '/payments/success',
                'cancel_url' => config('app.frontend_url') . '/payments/cancel',
            ];

            // Make API request to CTechPay
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
                'X-Environment' => $this->environment,
            ])->post($this->apiUrl . '/payments', $requestData);

            if ($response->successful()) {
                $responseData = $response->json();
                
                $payment->update([
                    'external_reference' => $responseData['transaction_id'] ?? null,
                    'status' => Payment::STATUS_PROCESSING,
                    'gateway_response' => $responseData,
                ]);

                // Log the payment initiation
                AuditLog::logAction(
                    AuditLog::ACTION_PAYMENT,
                    $application->user,
                    Payment::class,
                    $payment->id,
                    'Payment initiated via CTechPay'
                );

                return [
                    'success' => true,
                    'payment' => $payment,
                    'gateway_response' => $responseData,
                ];
            } else {
                $payment->update([
                    'status' => Payment::STATUS_FAILED,
                    'failure_reason' => 'Gateway error: ' . $response->body(),
                    'failed_at' => now(),
                ]);

                Log::error('CTechPay payment failed', [
                    'payment_id' => $payment->id,
                    'response' => $response->body(),
                ]);

                return [
                    'success' => false,
                    'message' => 'Payment processing failed',
                    'payment' => $payment,
                ];
            }
        } catch (\Exception $e) {
            Log::error('Payment processing exception', [
                'application_id' => $application->id,
                'error' => $e->getMessage(),
            ]);

            if (isset($payment)) {
                $payment->update([
                    'status' => Payment::STATUS_FAILED,
                    'failure_reason' => 'System error: ' . $e->getMessage(),
                    'failed_at' => now(),
                ]);
            }

            return [
                'success' => false,
                'message' => 'Payment processing failed due to system error',
                'payment' => $payment ?? null,
            ];
        }
    }

    /**
     * Handle payment webhook
     */
    public function handleWebhook(array $webhookData)
    {
        try {
            // Verify webhook signature
            if (!$this->verifyWebhookSignature($webhookData)) {
                Log::warning('Invalid webhook signature', $webhookData);
                return false;
            }

            $externalReference = $webhookData['transaction_id'] ?? null;
            if (!$externalReference) {
                Log::warning('Webhook missing transaction_id', $webhookData);
                return false;
            }

            $payment = Payment::where('external_reference', $externalReference)->first();
            if (!$payment) {
                Log::warning('Payment not found for webhook', ['transaction_id' => $externalReference]);
                return false;
            }

            $oldStatus = $payment->status;
            $newStatus = $this->mapWebhookStatus($webhookData['status'] ?? 'unknown');

            $updateData = [
                'status' => $newStatus,
                'webhook_data' => $webhookData,
            ];

            if ($newStatus === Payment::STATUS_COMPLETED) {
                $updateData['processed_at'] = now();
                
                // Update application status to payment confirmed
                $payment->application->update([
                    'status' => Application::STATUS_PAYMENT_CONFIRMED
                ]);

                // Log application status change
                AuditLog::logAction(
                    AuditLog::ACTION_STATUS_CHANGE,
                    null,
                    Application::class,
                    $payment->application_id,
                    'Application status updated to payment confirmed via webhook'
                );
            } elseif ($newStatus === Payment::STATUS_FAILED) {
                $updateData['failed_at'] = now();
                $updateData['failure_reason'] = $webhookData['failure_reason'] ?? 'Payment failed';
            }

            $payment->update($updateData);

            // Log the webhook processing
            AuditLog::logAction(
                AuditLog::ACTION_PAYMENT,
                null,
                Payment::class,
                $payment->id,
                "Payment status updated from {$oldStatus} to {$newStatus} via webhook"
            );

            return true;
        } catch (\Exception $e) {
            Log::error('Webhook processing exception', [
                'webhook_data' => $webhookData,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Map payment method to CTechPay format
     */
    protected function mapPaymentMethod(string $method): string
    {
        return match($method) {
            Payment::METHOD_AIRTEL_MONEY => 'airtel_money',
            Payment::METHOD_TNM_MPAMBA => 'tnm_mpamba',
            default => $method,
        };
    }

    /**
     * Map webhook status to payment status
     */
    protected function mapWebhookStatus(string $webhookStatus): string
    {
        return match(strtolower($webhookStatus)) {
            'success', 'completed', 'paid' => Payment::STATUS_COMPLETED,
            'failed', 'declined', 'error' => Payment::STATUS_FAILED,
            'cancelled', 'canceled' => Payment::STATUS_CANCELLED,
            'pending', 'processing' => Payment::STATUS_PROCESSING,
            default => Payment::STATUS_FAILED,
        };
    }

    /**
     * Verify webhook signature
     */
    protected function verifyWebhookSignature(array $webhookData): bool
    {
        $signature = request()->header('X-CTechPay-Signature');
        if (!$signature) {
            return false;
        }

        $expectedSignature = hash_hmac('sha256', json_encode($webhookData), $this->secretKey);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Get payment status from CTechPay
     */
    public function getPaymentStatus(Payment $payment)
    {
        try {
            if (!$payment->external_reference) {
                return null;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $this->apiKey,
                'X-Environment' => $this->environment,
            ])->get($this->apiUrl . '/payments/' . $payment->external_reference);

            if ($response->successful()) {
                return $response->json();
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Failed to get payment status', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Generate payment receipt
     */
    public function generateReceipt(Payment $payment)
    {
        // This would integrate with a PDF generation service
        // For now, return receipt data
        return [
            'payment_reference' => $payment->payment_reference,
            'amount' => $payment->getFormattedAmount(),
            'payment_method' => Payment::getPaymentMethods()[$payment->payment_method] ?? $payment->payment_method,
            'status' => Payment::getStatuses()[$payment->status] ?? $payment->status,
            'date' => $payment->processed_at?->format('Y-m-d H:i:s') ?? $payment->created_at->format('Y-m-d H:i:s'),
            'application_number' => $payment->application->application_number,
            'payer_name' => $payment->payer_name,
            'phone_number' => $payment->phone_number,
        ];
    }
}
