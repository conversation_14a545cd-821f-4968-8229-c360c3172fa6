<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Storage;

class ApplicationDocument extends Model
{
    use HasFactory;

    // Document types
    const TYPE_NATIONAL_ID = 'national_id';
    const TYPE_PROPERTY_DEED = 'property_deed';
    const TYPE_LEASE_AGREEMENT = 'lease_agreement';
    const TYPE_AUTHORIZATION_LETTER = 'authorization_letter';
    const TYPE_SITE_PLAN = 'site_plan';
    const TYPE_PHOTO = 'photo';
    const TYPE_OTHER = 'other';

    protected $fillable = [
        'application_id',
        'document_type',
        'file_name',
        'file_path',
        'file_size',
        'mime_type',
        'description',
        'uploaded_by',
    ];

    protected function casts(): array
    {
        return [
            'file_size' => 'integer',
        ];
    }

    /**
     * Get the application this document belongs to
     */
    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    /**
     * Get the user who uploaded this document
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get available document types
     */
    public static function getDocumentTypes(): array
    {
        return [
            self::TYPE_NATIONAL_ID => 'National ID',
            self::TYPE_PROPERTY_DEED => 'Property Deed',
            self::TYPE_LEASE_AGREEMENT => 'Lease Agreement',
            self::TYPE_AUTHORIZATION_LETTER => 'Authorization Letter',
            self::TYPE_SITE_PLAN => 'Site Plan',
            self::TYPE_PHOTO => 'Photo',
            self::TYPE_OTHER => 'Other',
        ];
    }

    /**
     * Get file URL
     */
    public function getFileUrl(): string
    {
        return Storage::url($this->file_path);
    }

    /**
     * Get formatted file size
     */
    public function getFormattedFileSize(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Check if file is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->mime_type, 'image/');
    }

    /**
     * Check if file is a PDF
     */
    public function isPdf(): bool
    {
        return $this->mime_type === 'application/pdf';
    }

    /**
     * Delete file from storage when model is deleted
     */
    protected static function boot()
    {
        parent::boot();

        static::deleting(function ($document) {
            if (Storage::exists($document->file_path)) {
                Storage::delete($document->file_path);
            }
        });
    }
}
