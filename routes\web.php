<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', function () {
        $user = auth()->user();

        // Get basic stats for the dashboard
        $stats = [
            'applications' => [
                'total' => $user->isCustomer() ? $user->applications()->count() : \App\Models\Application::count(),
                'pending' => $user->isCustomer() ? $user->applications()->where('status', 'submitted')->count() : \App\Models\Application::where('status', 'submitted')->count(),
                'approved' => $user->isCustomer() ? $user->applications()->where('status', 'approved')->count() : \App\Models\Application::where('status', 'approved')->count(),
                'completed' => $user->isCustomer() ? $user->applications()->where('status', 'completed')->count() : \App\Models\Application::where('status', 'completed')->count(),
            ],
            'payments' => [
                'total' => $user->isCustomer() ? $user->payments()->count() : \App\Models\Payment::count(),
                'pending' => $user->isCustomer() ? $user->payments()->where('status', 'pending')->count() : \App\Models\Payment::where('status', 'pending')->count(),
                'completed' => $user->isCustomer() ? $user->payments()->where('status', 'completed')->count() : \App\Models\Payment::where('status', 'completed')->count(),
                'total_amount' => $user->isCustomer() ? $user->payments()->where('status', 'completed')->sum('amount') : \App\Models\Payment::where('status', 'completed')->sum('amount'),
            ],
        ];

        // Get recent applications
        $recent_applications = $user->isCustomer()
            ? $user->applications()->with('user')->latest()->take(5)->get()
            : \App\Models\Application::with('user')->latest()->take(5)->get();

        // Get recent payments
        $recent_payments = $user->isCustomer()
            ? $user->payments()->with('application')->latest()->take(5)->get()
            : \App\Models\Payment::with(['application', 'user'])->latest()->take(5)->get();

        return Inertia::render('dashboard', [
            'stats' => $stats,
            'recent_applications' => $recent_applications,
            'recent_payments' => $recent_payments,
        ]);
    })->name('dashboard');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
