<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ApplicationStatusHistory extends Model
{
    use HasFactory;

    protected $fillable = [
        'application_id',
        'user_id',
        'old_status',
        'new_status',
        'notes',
        'changed_at',
    ];

    protected function casts(): array
    {
        return [
            'changed_at' => 'datetime',
        ];
    }

    /**
     * Get the application this status history belongs to
     */
    public function application(): BelongsTo
    {
        return $this->belongsTo(Application::class);
    }

    /**
     * Get the user who changed the status
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get formatted status names
     */
    public function getFormattedOldStatus(): string
    {
        $statuses = Application::getStatuses();
        return $statuses[$this->old_status] ?? $this->old_status;
    }

    public function getFormattedNewStatus(): string
    {
        $statuses = Application::getStatuses();
        return $statuses[$this->new_status] ?? $this->new_status;
    }

    /**
     * Boot the model
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($statusHistory) {
            if (empty($statusHistory->changed_at)) {
                $statusHistory->changed_at = now();
            }
        });
    }
}
